"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileOperations = exports.FileOperations = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const glob_1 = require("glob");
const logger_1 = require("@/utils/logger");
const config_1 = require("@/config");
class FileOperations {
    static instance;
    static getInstance() {
        if (!FileOperations.instance) {
            FileOperations.instance = new FileOperations();
        }
        return FileOperations.instance;
    }
    async readFile(filePath, context) {
        try {
            this.validatePath(filePath, context);
            const absolutePath = this.resolvePath(filePath, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(absolutePath)) {
                return {
                    success: false,
                    message: `File does not exist: ${filePath}`,
                    error: 'FILE_NOT_FOUND',
                };
            }
            const stat = await fs_extra_1.default.stat(absolutePath);
            if (!stat.isFile()) {
                return {
                    success: false,
                    message: `Path is not a file: ${filePath}`,
                    error: 'NOT_A_FILE',
                };
            }
            const content = await fs_extra_1.default.readFile(absolutePath, 'utf-8');
            logger_1.logger.debug(`File read successfully`, { filePath, size: content.length }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `File read successfully: ${filePath}`,
                data: {
                    content,
                    size: stat.size,
                    lastModified: stat.mtime,
                    path: filePath,
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to read file: ${filePath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to read file: ${filePath}`,
                error: error.message,
            };
        }
    }
    async writeFile(filePath, content, context, options = {}) {
        try {
            this.validatePath(filePath, context);
            const absolutePath = this.resolvePath(filePath, context.workingDirectory);
            const { overwrite = false, createDirs = true } = options;
            if (fs_extra_1.default.existsSync(absolutePath) && !overwrite) {
                return {
                    success: false,
                    message: `File already exists: ${filePath}. Use overwrite option to replace.`,
                    error: 'FILE_EXISTS',
                };
            }
            if (createDirs) {
                await fs_extra_1.default.ensureDir(path_1.default.dirname(absolutePath));
            }
            await fs_extra_1.default.writeFile(absolutePath, content, 'utf-8');
            const stat = await fs_extra_1.default.stat(absolutePath);
            logger_1.logger.info(`File written successfully`, {
                filePath,
                size: content.length,
                overwrite
            }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `File written successfully: ${filePath}`,
                data: {
                    path: filePath,
                    size: stat.size,
                    created: !overwrite,
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to write file: ${filePath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to write file: ${filePath}`,
                error: error.message,
            };
        }
    }
    async deleteFile(filePath, context) {
        try {
            this.validatePath(filePath, context);
            const absolutePath = this.resolvePath(filePath, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(absolutePath)) {
                return {
                    success: false,
                    message: `File does not exist: ${filePath}`,
                    error: 'FILE_NOT_FOUND',
                };
            }
            await fs_extra_1.default.remove(absolutePath);
            logger_1.logger.info(`File deleted successfully`, { filePath }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `File deleted successfully: ${filePath}`,
                data: { path: filePath },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete file: ${filePath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to delete file: ${filePath}`,
                error: error.message,
            };
        }
    }
    async copyFile(sourcePath, destPath, context, options = {}) {
        try {
            this.validatePath(sourcePath, context);
            this.validatePath(destPath, context);
            const absoluteSource = this.resolvePath(sourcePath, context.workingDirectory);
            const absoluteDest = this.resolvePath(destPath, context.workingDirectory);
            const { overwrite = false } = options;
            if (!fs_extra_1.default.existsSync(absoluteSource)) {
                return {
                    success: false,
                    message: `Source file does not exist: ${sourcePath}`,
                    error: 'SOURCE_NOT_FOUND',
                };
            }
            if (fs_extra_1.default.existsSync(absoluteDest) && !overwrite) {
                return {
                    success: false,
                    message: `Destination file already exists: ${destPath}. Use overwrite option to replace.`,
                    error: 'DEST_EXISTS',
                };
            }
            await fs_extra_1.default.copy(absoluteSource, absoluteDest, { overwrite });
            logger_1.logger.info(`File copied successfully`, {
                sourcePath,
                destPath,
                overwrite
            }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `File copied successfully: ${sourcePath} -> ${destPath}`,
                data: {
                    sourcePath,
                    destPath,
                    overwrite,
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to copy file: ${sourcePath} -> ${destPath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to copy file: ${sourcePath} -> ${destPath}`,
                error: error.message,
            };
        }
    }
    async moveFile(sourcePath, destPath, context) {
        try {
            this.validatePath(sourcePath, context);
            this.validatePath(destPath, context);
            const absoluteSource = this.resolvePath(sourcePath, context.workingDirectory);
            const absoluteDest = this.resolvePath(destPath, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(absoluteSource)) {
                return {
                    success: false,
                    message: `Source file does not exist: ${sourcePath}`,
                    error: 'SOURCE_NOT_FOUND',
                };
            }
            if (fs_extra_1.default.existsSync(absoluteDest)) {
                return {
                    success: false,
                    message: `Destination file already exists: ${destPath}`,
                    error: 'DEST_EXISTS',
                };
            }
            await fs_extra_1.default.move(absoluteSource, absoluteDest);
            logger_1.logger.info(`File moved successfully`, {
                sourcePath,
                destPath
            }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `File moved successfully: ${sourcePath} -> ${destPath}`,
                data: {
                    sourcePath,
                    destPath,
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to move file: ${sourcePath} -> ${destPath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to move file: ${sourcePath} -> ${destPath}`,
                error: error.message,
            };
        }
    }
    async createDirectory(dirPath, context) {
        try {
            this.validatePath(dirPath, context);
            const absolutePath = this.resolvePath(dirPath, context.workingDirectory);
            if (fs_extra_1.default.existsSync(absolutePath)) {
                return {
                    success: false,
                    message: `Directory already exists: ${dirPath}`,
                    error: 'DIR_EXISTS',
                };
            }
            await fs_extra_1.default.ensureDir(absolutePath);
            logger_1.logger.info(`Directory created successfully`, { dirPath }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `Directory created successfully: ${dirPath}`,
                data: { path: dirPath },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to create directory: ${dirPath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to create directory: ${dirPath}`,
                error: error.message,
            };
        }
    }
    async listDirectory(dirPath, context) {
        try {
            this.validatePath(dirPath, context);
            const absolutePath = this.resolvePath(dirPath, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(absolutePath)) {
                return {
                    success: false,
                    message: `Directory does not exist: ${dirPath}`,
                    error: 'DIR_NOT_FOUND',
                };
            }
            const stat = await fs_extra_1.default.stat(absolutePath);
            if (!stat.isDirectory()) {
                return {
                    success: false,
                    message: `Path is not a directory: ${dirPath}`,
                    error: 'NOT_A_DIRECTORY',
                };
            }
            const items = await fs_extra_1.default.readdir(absolutePath, { withFileTypes: true });
            const result = await Promise.all(items.map(async (item) => {
                const itemPath = path_1.default.join(absolutePath, item.name);
                const itemStat = await fs_extra_1.default.stat(itemPath);
                return {
                    name: item.name,
                    type: item.isDirectory() ? 'directory' : 'file',
                    size: itemStat.size,
                    lastModified: itemStat.mtime,
                    permissions: {
                        readable: !!(itemStat.mode & parseInt('400', 8)),
                        writable: !!(itemStat.mode & parseInt('200', 8)),
                        executable: !!(itemStat.mode & parseInt('100', 8)),
                    },
                };
            }));
            logger_1.logger.debug(`Directory listed successfully`, {
                dirPath,
                itemCount: result.length
            }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `Directory listed successfully: ${dirPath}`,
                data: {
                    path: dirPath,
                    items: result,
                    count: result.length,
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to list directory: ${dirPath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to list directory: ${dirPath}`,
                error: error.message,
            };
        }
    }
    async searchFiles(pattern, context, options = {}) {
        try {
            const { directory = '.', includeContent = false, maxResults = 100, fileTypes = [], } = options;
            this.validatePath(directory, context);
            const searchDir = this.resolvePath(directory, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(searchDir)) {
                return {
                    success: false,
                    message: `Search directory does not exist: ${directory}`,
                    error: 'DIR_NOT_FOUND',
                };
            }
            const excludePatterns = config_1.config.getConfig().context.excludePatterns;
            let globPattern = pattern;
            // Add file type filter if specified
            if (fileTypes.length > 0) {
                const extensions = fileTypes.map(ext => ext.startsWith('.') ? ext : `.${ext}`);
                globPattern = `**/*{${extensions.join(',')}}`;
            }
            const files = await (0, glob_1.glob)(globPattern, {
                cwd: searchDir,
                ignore: excludePatterns,
                absolute: false,
                nodir: true,
            });
            const results = [];
            for (const file of files.slice(0, maxResults)) {
                const filePath = path_1.default.join(searchDir, file);
                const stat = await fs_extra_1.default.stat(filePath);
                const result = {
                    path: file,
                    size: stat.size,
                    lastModified: stat.mtime,
                    extension: path_1.default.extname(file),
                };
                if (includeContent && stat.size < 100000) { // Only read files < 100KB
                    try {
                        result.content = await fs_extra_1.default.readFile(filePath, 'utf-8');
                    }
                    catch (error) {
                        result.contentError = 'Failed to read content';
                    }
                }
                results.push(result);
            }
            logger_1.logger.debug(`File search completed`, {
                pattern,
                directory,
                resultCount: results.length
            }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `Found ${results.length} files matching pattern: ${pattern}`,
                data: {
                    pattern,
                    directory,
                    results,
                    totalFound: files.length,
                    truncated: files.length > maxResults,
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to search files: ${pattern}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to search files: ${pattern}`,
                error: error.message,
            };
        }
    }
    async grepFiles(searchText, context, options = {}) {
        try {
            const { directory = '.', filePattern = '**/*', caseSensitive = false, maxResults = 100, contextLines = 2, } = options;
            this.validatePath(directory, context);
            const searchDir = this.resolvePath(directory, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(searchDir)) {
                return {
                    success: false,
                    message: `Search directory does not exist: ${directory}`,
                    error: 'DIR_NOT_FOUND',
                };
            }
            const excludePatterns = config_1.config.getConfig().context.excludePatterns;
            const files = await (0, glob_1.glob)(filePattern, {
                cwd: searchDir,
                ignore: excludePatterns,
                absolute: false,
                nodir: true,
            });
            const results = [];
            const regex = new RegExp(searchText, caseSensitive ? 'g' : 'gi');
            for (const file of files) {
                if (results.length >= maxResults)
                    break;
                const filePath = path_1.default.join(searchDir, file);
                const stat = await fs_extra_1.default.stat(filePath);
                // Skip large files
                if (stat.size > 1024 * 1024)
                    continue; // Skip files > 1MB
                try {
                    const content = await fs_extra_1.default.readFile(filePath, 'utf-8');
                    const lines = content.split('\n');
                    const matches = [];
                    for (let i = 0; i < lines.length; i++) {
                        if (regex.test(lines[i])) {
                            const start = Math.max(0, i - contextLines);
                            const end = Math.min(lines.length - 1, i + contextLines);
                            matches.push({
                                lineNumber: i + 1,
                                line: lines[i],
                                context: {
                                    before: lines.slice(start, i),
                                    after: lines.slice(i + 1, end + 1),
                                },
                            });
                        }
                    }
                    if (matches.length > 0) {
                        results.push({
                            file,
                            matches,
                            matchCount: matches.length,
                        });
                    }
                }
                catch (error) {
                    // Skip files that can't be read as text
                    continue;
                }
            }
            logger_1.logger.debug(`Grep search completed`, {
                searchText,
                directory,
                fileCount: files.length,
                resultCount: results.length
            }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `Found ${results.length} files with matches for: ${searchText}`,
                data: {
                    searchText,
                    directory,
                    results,
                    totalMatches: results.reduce((sum, r) => sum + r.matchCount, 0),
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to grep files: ${searchText}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to grep files: ${searchText}`,
                error: error.message,
            };
        }
    }
    async getFileInfo(filePath, context) {
        try {
            this.validatePath(filePath, context);
            const absolutePath = this.resolvePath(filePath, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(absolutePath)) {
                return {
                    success: false,
                    message: `File does not exist: ${filePath}`,
                    error: 'FILE_NOT_FOUND',
                };
            }
            const stat = await fs_extra_1.default.stat(absolutePath);
            return {
                success: true,
                message: `File info retrieved: ${filePath}`,
                data: {
                    path: filePath,
                    absolutePath,
                    size: stat.size,
                    isFile: stat.isFile(),
                    isDirectory: stat.isDirectory(),
                    lastModified: stat.mtime,
                    lastAccessed: stat.atime,
                    created: stat.birthtime,
                    permissions: {
                        readable: !!(stat.mode & parseInt('400', 8)),
                        writable: !!(stat.mode & parseInt('200', 8)),
                        executable: !!(stat.mode & parseInt('100', 8)),
                        mode: stat.mode.toString(8),
                    },
                    extension: path_1.default.extname(filePath),
                    basename: path_1.default.basename(filePath),
                    dirname: path_1.default.dirname(filePath),
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get file info: ${filePath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to get file info: ${filePath}`,
                error: error.message,
            };
        }
    }
    async setPermissions(filePath, permissions, context) {
        try {
            this.validatePath(filePath, context);
            const absolutePath = this.resolvePath(filePath, context.workingDirectory);
            if (!fs_extra_1.default.existsSync(absolutePath)) {
                return {
                    success: false,
                    message: `File does not exist: ${filePath}`,
                    error: 'FILE_NOT_FOUND',
                };
            }
            await fs_extra_1.default.chmod(absolutePath, permissions);
            logger_1.logger.info(`Permissions changed successfully`, {
                filePath,
                permissions
            }, 'FileOperations', context.sessionId);
            return {
                success: true,
                message: `Permissions changed successfully: ${filePath}`,
                data: {
                    path: filePath,
                    permissions,
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to change permissions: ${filePath}`, error, 'FileOperations', context.sessionId);
            return {
                success: false,
                message: `Failed to change permissions: ${filePath}`,
                error: error.message,
            };
        }
    }
    validatePath(filePath, context) {
        const cliConfig = config_1.config.getConfig();
        if (!cliConfig.tools.allowFileOperations) {
            throw new Error('File operations are disabled in configuration');
        }
        // Check for restricted paths
        const absolutePath = this.resolvePath(filePath, context.workingDirectory);
        for (const restrictedPath of cliConfig.tools.restrictedPaths) {
            const resolvedRestricted = path_1.default.resolve(restrictedPath);
            if (absolutePath.startsWith(resolvedRestricted)) {
                throw new Error(`Access to restricted path: ${filePath}`);
            }
        }
        // Prevent path traversal attacks
        if (filePath.includes('..') || filePath.includes('~')) {
            throw new Error(`Invalid path: ${filePath}`);
        }
    }
    resolvePath(filePath, workingDirectory) {
        if (path_1.default.isAbsolute(filePath)) {
            return path_1.default.normalize(filePath);
        }
        return path_1.default.resolve(workingDirectory, filePath);
    }
}
exports.FileOperations = FileOperations;
exports.fileOperations = FileOperations.getInstance();
//# sourceMappingURL=file-ops.js.map