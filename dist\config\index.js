"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = exports.ConfigManager = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const os_1 = __importDefault(require("os"));
const zod_1 = require("zod");
const ConfigSchema = zod_1.z.object({
    defaultProvider: zod_1.z.enum(['deepseek', 'ollama']).default('deepseek'),
    providers: zod_1.z.object({
        deepseek: zod_1.z.object({
            apiKey: zod_1.z.string().optional(),
            baseUrl: zod_1.z.string().default('https://api.deepseek.com'),
            defaultModel: zod_1.z.string().default('deepseek-chat'),
        }),
        ollama: zod_1.z.object({
            baseUrl: zod_1.z.string().default('http://localhost:11434'),
            defaultModel: zod_1.z.string().default('llama3.2'),
        }),
    }),
    session: zod_1.z.object({
        autoSave: zod_1.z.boolean().default(true),
        maxHistory: zod_1.z.number().default(1000),
        persistContext: zod_1.z.boolean().default(true),
    }),
    context: zod_1.z.object({
        autoIndex: zod_1.z.boolean().default(true),
        watchFiles: zod_1.z.boolean().default(true),
        maxFileSize: zod_1.z.number().default(1024 * 1024), // 1MB
        excludePatterns: zod_1.z.array(zod_1.z.string()).default([
            'node_modules/**',
            '.git/**',
            'dist/**',
            'build/**',
            '*.log',
            '.env*',
            '*.tmp',
            '*.cache',
        ]),
    }),
    tools: zod_1.z.object({
        allowShellExecution: zod_1.z.boolean().default(true),
        allowFileOperations: zod_1.z.boolean().default(true),
        allowNetworkAccess: zod_1.z.boolean().default(true),
        restrictedPaths: zod_1.z.array(zod_1.z.string()).default([]),
    }),
});
class ConfigManager {
    static instance;
    config;
    configPath;
    constructor() {
        this.configPath = path_1.default.join(os_1.default.homedir(), '.agentic-cli', 'config.json');
        this.config = this.loadConfig();
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    loadConfig() {
        try {
            if (fs_extra_1.default.existsSync(this.configPath)) {
                const configData = fs_extra_1.default.readJsonSync(this.configPath);
                return ConfigSchema.parse(configData);
            }
        }
        catch (error) {
            console.warn('Failed to load config, using defaults:', error);
        }
        return this.getDefaultConfig();
    }
    getDefaultConfig() {
        return ConfigSchema.parse({});
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(updates) {
        this.config = ConfigSchema.parse({ ...this.config, ...updates });
        this.saveConfig();
    }
    saveConfig() {
        try {
            fs_extra_1.default.ensureDirSync(path_1.default.dirname(this.configPath));
            fs_extra_1.default.writeJsonSync(this.configPath, this.config, { spaces: 2 });
        }
        catch (error) {
            console.error('Failed to save config:', error);
        }
    }
    resetConfig() {
        this.config = this.getDefaultConfig();
        this.saveConfig();
    }
    getConfigPath() {
        return this.configPath;
    }
    getDataDirectory() {
        return path_1.default.join(os_1.default.homedir(), '.agentic-cli');
    }
    getSessionsDirectory() {
        return path_1.default.join(this.getDataDirectory(), 'sessions');
    }
    getCacheDirectory() {
        return path_1.default.join(this.getDataDirectory(), 'cache');
    }
    getLogsDirectory() {
        return path_1.default.join(this.getDataDirectory(), 'logs');
    }
    ensureDirectories() {
        const directories = [
            this.getDataDirectory(),
            this.getSessionsDirectory(),
            this.getCacheDirectory(),
            this.getLogsDirectory(),
        ];
        directories.forEach(dir => {
            fs_extra_1.default.ensureDirSync(dir);
        });
    }
    validateProviderConfig(provider) {
        const config = this.config.providers[provider];
        if (provider === 'deepseek') {
            return !!config.apiKey || !!process.env.DEEPSEEK_API_KEY;
        }
        if (provider === 'ollama') {
            return !!config.baseUrl;
        }
        return false;
    }
    getProviderConfig(provider) {
        const config = this.config.providers[provider];
        if (provider === 'deepseek') {
            return {
                ...config,
                apiKey: config.apiKey || process.env.DEEPSEEK_API_KEY,
            };
        }
        return config;
    }
    setProviderApiKey(provider, apiKey) {
        if (provider === 'deepseek') {
            this.updateConfig({
                providers: {
                    ...this.config.providers,
                    deepseek: {
                        ...this.config.providers.deepseek,
                        apiKey,
                    },
                },
            });
        }
    }
    getEnvironmentVariables() {
        return {
            AGENTIC_CLI_CONFIG_PATH: this.configPath,
            AGENTIC_CLI_DATA_DIR: this.getDataDirectory(),
            AGENTIC_CLI_SESSIONS_DIR: this.getSessionsDirectory(),
            AGENTIC_CLI_CACHE_DIR: this.getCacheDirectory(),
            AGENTIC_CLI_LOGS_DIR: this.getLogsDirectory(),
        };
    }
}
exports.ConfigManager = ConfigManager;
exports.config = ConfigManager.getInstance();
//# sourceMappingURL=index.js.map