"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toolRegistry = exports.ToolRegistry = exports.allTools = exports.getSessionInfoTool = exports.getProjectContextTool = exports.listProcessesTool = exports.killProcessTool = exports.executeScriptTool = exports.executeCommandTool = exports.setPermissionsTool = exports.getFileInfoTool = exports.grepFilesTool = exports.searchFilesTool = exports.listDirectoryTool = exports.createDirectoryTool = exports.moveFileTool = exports.copyFileTool = exports.deleteFileTool = exports.writeFileTool = exports.readFileTool = void 0;
const zod_1 = require("zod");
const file_ops_1 = require("@/operations/file-ops");
const shell_ops_1 = require("@/operations/shell-ops");
const logger_1 = require("@/utils/logger");
// File operation tools
exports.readFileTool = {
    name: 'read_file',
    description: 'Read the contents of a file',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file to read'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.readFile(params.filePath, context);
    },
};
exports.writeFileTool = {
    name: 'write_file',
    description: 'Write content to a file',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file to write'),
        content: zod_1.z.string().describe('Content to write to the file'),
        overwrite: zod_1.z.boolean().optional().describe('Whether to overwrite existing file'),
        createDirs: zod_1.z.boolean().optional().describe('Whether to create parent directories'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.writeFile(params.filePath, params.content, context, { overwrite: params.overwrite, createDirs: params.createDirs });
    },
};
exports.deleteFileTool = {
    name: 'delete_file',
    description: 'Delete a file or directory',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file or directory to delete'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.deleteFile(params.filePath, context);
    },
};
exports.copyFileTool = {
    name: 'copy_file',
    description: 'Copy a file from source to destination',
    parameters: zod_1.z.object({
        sourcePath: zod_1.z.string().describe('Source file path'),
        destPath: zod_1.z.string().describe('Destination file path'),
        overwrite: zod_1.z.boolean().optional().describe('Whether to overwrite existing file'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.copyFile(params.sourcePath, params.destPath, context, { overwrite: params.overwrite });
    },
};
exports.moveFileTool = {
    name: 'move_file',
    description: 'Move a file from source to destination',
    parameters: zod_1.z.object({
        sourcePath: zod_1.z.string().describe('Source file path'),
        destPath: zod_1.z.string().describe('Destination file path'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.moveFile(params.sourcePath, params.destPath, context);
    },
};
exports.createDirectoryTool = {
    name: 'create_directory',
    description: 'Create a new directory',
    parameters: zod_1.z.object({
        dirPath: zod_1.z.string().describe('Path of the directory to create'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.createDirectory(params.dirPath, context);
    },
};
exports.listDirectoryTool = {
    name: 'list_directory',
    description: 'List contents of a directory',
    parameters: zod_1.z.object({
        dirPath: zod_1.z.string().describe('Path of the directory to list'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.listDirectory(params.dirPath, context);
    },
};
exports.searchFilesTool = {
    name: 'search_files',
    description: 'Search for files matching a pattern',
    parameters: zod_1.z.object({
        pattern: zod_1.z.string().describe('Glob pattern to search for files'),
        directory: zod_1.z.string().optional().describe('Directory to search in (default: current)'),
        includeContent: zod_1.z.boolean().optional().describe('Whether to include file content in results'),
        maxResults: zod_1.z.number().optional().describe('Maximum number of results to return'),
        fileTypes: zod_1.z.array(zod_1.z.string()).optional().describe('File extensions to filter by'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.searchFiles(params.pattern, context, {
            directory: params.directory,
            includeContent: params.includeContent,
            maxResults: params.maxResults,
            fileTypes: params.fileTypes,
        });
    },
};
exports.grepFilesTool = {
    name: 'grep_files',
    description: 'Search for text content within files',
    parameters: zod_1.z.object({
        searchText: zod_1.z.string().describe('Text to search for'),
        directory: zod_1.z.string().optional().describe('Directory to search in (default: current)'),
        filePattern: zod_1.z.string().optional().describe('File pattern to search within'),
        caseSensitive: zod_1.z.boolean().optional().describe('Whether search is case sensitive'),
        maxResults: zod_1.z.number().optional().describe('Maximum number of results to return'),
        contextLines: zod_1.z.number().optional().describe('Number of context lines to include'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.grepFiles(params.searchText, context, {
            directory: params.directory,
            filePattern: params.filePattern,
            caseSensitive: params.caseSensitive,
            maxResults: params.maxResults,
            contextLines: params.contextLines,
        });
    },
};
exports.getFileInfoTool = {
    name: 'get_file_info',
    description: 'Get detailed information about a file or directory',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file or directory'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.getFileInfo(params.filePath, context);
    },
};
exports.setPermissionsTool = {
    name: 'set_permissions',
    description: 'Set file or directory permissions',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file or directory'),
        permissions: zod_1.z.union([zod_1.z.string(), zod_1.z.number()]).describe('Permissions to set (e.g., "755" or 0o755)'),
    }),
    execute: async (params, context) => {
        return await file_ops_1.fileOperations.setPermissions(params.filePath, params.permissions, context);
    },
};
// Shell operation tools
exports.executeCommandTool = {
    name: 'execute_command',
    description: 'Execute a shell command',
    parameters: zod_1.z.object({
        command: zod_1.z.string().describe('Command to execute'),
        timeout: zod_1.z.number().optional().describe('Timeout in milliseconds (default: 30000)'),
        cwd: zod_1.z.string().optional().describe('Working directory for the command'),
        env: zod_1.z.record(zod_1.z.string()).optional().describe('Environment variables'),
        detached: zod_1.z.boolean().optional().describe('Whether to run command in detached mode'),
    }),
    execute: async (params, context) => {
        return await shell_ops_1.shellOperations.executeCommand(params.command, context, {
            timeout: params.timeout,
            cwd: params.cwd,
            env: params.env,
            detached: params.detached,
        });
    },
};
exports.executeScriptTool = {
    name: 'execute_script',
    description: 'Execute a script with specified interpreter',
    parameters: zod_1.z.object({
        script: zod_1.z.string().describe('Script content to execute'),
        interpreter: zod_1.z.string().optional().describe('Script interpreter (default: bash)'),
        timeout: zod_1.z.number().optional().describe('Timeout in milliseconds'),
        cwd: zod_1.z.string().optional().describe('Working directory for the script'),
        env: zod_1.z.record(zod_1.z.string()).optional().describe('Environment variables'),
    }),
    execute: async (params, context) => {
        return await shell_ops_1.shellOperations.executeScript(params.script, context, {
            interpreter: params.interpreter,
            timeout: params.timeout,
            cwd: params.cwd,
            env: params.env,
        });
    },
};
exports.killProcessTool = {
    name: 'kill_process',
    description: 'Kill a running process by PID',
    parameters: zod_1.z.object({
        pid: zod_1.z.number().describe('Process ID to kill'),
    }),
    execute: async (params, context) => {
        const success = shell_ops_1.shellOperations.killProcess(params.pid);
        return {
            success,
            message: success
                ? `Process ${params.pid} killed successfully`
                : `Failed to kill process ${params.pid} or process not found`,
            data: { pid: params.pid, killed: success },
        };
    },
};
exports.listProcessesTool = {
    name: 'list_processes',
    description: 'List currently running processes started by the agent',
    parameters: zod_1.z.object({}),
    execute: async (params, context) => {
        const processes = shell_ops_1.shellOperations.getRunningProcesses();
        return {
            success: true,
            message: `Found ${processes.length} running processes`,
            data: { processes, count: processes.length },
        };
    },
};
// Context and session tools
exports.getProjectContextTool = {
    name: 'get_project_context',
    description: 'Get current project context and structure',
    parameters: zod_1.z.object({}),
    execute: async (params, context) => {
        return {
            success: true,
            message: 'Project context retrieved',
            data: {
                workingDirectory: context.workingDirectory,
                projectContext: context.projectContext,
                environment: context.environment,
            },
        };
    },
};
exports.getSessionInfoTool = {
    name: 'get_session_info',
    description: 'Get current session information',
    parameters: zod_1.z.object({}),
    execute: async (params, context) => {
        return {
            success: true,
            message: 'Session information retrieved',
            data: {
                sessionId: context.sessionId,
                workingDirectory: context.workingDirectory,
                environment: Object.keys(context.environment).length,
            },
        };
    },
};
// Tool registry
exports.allTools = [
    // File operations
    exports.readFileTool,
    exports.writeFileTool,
    exports.deleteFileTool,
    exports.copyFileTool,
    exports.moveFileTool,
    exports.createDirectoryTool,
    exports.listDirectoryTool,
    exports.searchFilesTool,
    exports.grepFilesTool,
    exports.getFileInfoTool,
    exports.setPermissionsTool,
    // Shell operations
    exports.executeCommandTool,
    exports.executeScriptTool,
    exports.killProcessTool,
    exports.listProcessesTool,
    // Context operations
    exports.getProjectContextTool,
    exports.getSessionInfoTool,
];
class ToolRegistry {
    tools = new Map();
    constructor() {
        this.registerTools(exports.allTools);
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        logger_1.logger.debug(`Tool registered: ${tool.name}`, undefined, 'ToolRegistry');
    }
    registerTools(tools) {
        tools.forEach(tool => this.registerTool(tool));
    }
    getTool(name) {
        return this.tools.get(name);
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getToolNames() {
        return Array.from(this.tools.keys());
    }
    hasTools(name) {
        return this.tools.has(name);
    }
    getToolsByCategory(category) {
        return this.getAllTools().filter(tool => tool.name.startsWith(category) || tool.description.toLowerCase().includes(category));
    }
}
exports.ToolRegistry = ToolRegistry;
exports.toolRegistry = new ToolRegistry();
//# sourceMappingURL=index.js.map