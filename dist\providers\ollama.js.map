{"version": 3, "file": "ollama.js", "sourceRoot": "", "sources": ["../../src/providers/ollama.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAE7C,2CAAwC;AAExC,MAAa,cAAc;IACT,IAAI,GAAG,QAAQ,CAAC;IACxB,MAAM,CAAgB;IAE9B;QACE,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,MAAM,EAAE,uBAAuB;YACxC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;SACF,CAAC,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,MAAmB;QACvC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,QAAwB,EAAE,MAAmB;QACpE,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAExC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,GAAG,MAAM,CAAC,OAAO,WAAW,EAC5B;gBACE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACvC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,WAAW,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;iBACtC;aACF,CACF,CAAC;YAEF,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;YAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;aACzB,EAAE,gBAAgB,CAAC,CAAC;YAErB,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAE1D,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,CAAC,KAAK,sBAAsB,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,QAAwB,EACxB,KAAa,EACb,MAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAExC,gFAAgF;YAChF,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,gBAAgB,GAAG;gBACvB;oBACE,IAAI,EAAE,QAAiB;oBACvB,OAAO,EAAE,WAAW;iBACrB;gBACD,GAAG,QAAQ;aACZ,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,GAAG,MAAM,CAAC,OAAO,WAAW,EAC5B;gBACE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC;gBAC/C,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,WAAW,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;iBACtC;aACF,CACF,CAAC;YAEF,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;YAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,qCAAqC;YACrC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE5D,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,YAAY,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC;gBAClC,cAAc,EAAE,SAAS,CAAC,MAAM;aACjC,EAAE,gBAAgB,CAAC,CAAC;YAErB,OAAO;gBACL,OAAO;gBACP,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;aACxD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAE/D,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,CAAC,KAAK,sBAAsB,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAmB;QACpD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,WAAW,CAAC,CAAC;YACrE,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;YAC1C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;YAE7E,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,eAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,mCAAmC,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;gBACnG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,MAAmB;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,WAAW,EAAE;gBACnD,IAAI,EAAE,MAAM,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;QACxF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAwB;QAC7C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI;YAClD,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACrC,MAAM,gBAAgB,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,SAAS,IAAI,CAAC,IAAI;eAChB,IAAI,CAAC,WAAW;cACjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;QAC3E,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhB,OAAO;;;;;;;;;;;;;;;;EAgBT,gBAAgB;;8EAE4D,CAAC;IAC7E,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YAClE,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,MAAM,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC1D,OAAO;wBACL,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE;wBACjD,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC;4BAC9D,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE;4BAC5C,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gCACxB,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS;6BACnC;yBACF,CAAC,CAAC;qBACJ,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;QACpF,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,MAAW;QACjC,sCAAsC;QACtC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YACzC,MAAM,UAAU,GAAQ,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;gBAC/D,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC9C,IAAI,CAAE,KAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU;gBACV,QAAQ,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;aACrD,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YACzC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YACzC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACxC,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;aAC9C,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,WAAW;IACxC,CAAC;CACF;AArQD,wCAqQC"}